@echo off
title TaskScheduler - System Tool
color 07
setlocal enabledelayedexpansion

echo ========================================
echo    TaskScheduler - SYSTEM TOOL
echo    Version 1.0
echo ========================================
echo.

if exist "TaskScheduler.exe" (
    for %%A in ("TaskScheduler.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TaskScheduler.exe - !size_mb! MB
    )
    echo.
    echo 📋 Tool Information:
    echo - Name: TaskScheduler
    echo - Type: System Utility
    echo - Version: 1.0
    echo - Size: Optimized for performance
    echo.
    echo 🔧 Features:
    echo ✅ System monitoring
    echo ✅ Basic diagnostics
    echo ✅ Performance check
    echo ✅ Network status
    echo.
    echo Options:
    echo [1] Run TaskScheduler.exe
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🔧 STARTING SYSTEM TOOL
        echo Running basic system diagnostics...
        echo.
        set /p "confirm=Continue? (y/n): "
        if /i "!confirm!"=="y" (
            echo.
            echo 🚀 Starting TaskScheduler...
            start "" "TaskScheduler.exe"
            echo ✅ Tool launched successfully!
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TaskScheduler.exe not found!
)

echo.
pause