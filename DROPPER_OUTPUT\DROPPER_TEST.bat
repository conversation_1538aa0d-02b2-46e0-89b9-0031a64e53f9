@echo off
title Utility - Quick Tool
color 0F
setlocal enabledelayedexpansion

echo ========================================
echo    Utility - QUICK TOOL
echo    Lightweight Version
echo ========================================
echo.

if exist "Utility.exe" (
    for %%A in ("Utility.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ Utility.exe - !size_mb! MB
    )
    echo.
    echo 📋 Tool Information:
    echo - Name: Utility
    echo - Type: Quick Utility
    echo - Size: Ultra lightweight
    echo - Purpose: Basic system check
    echo.
    echo 🚀 Features:
    echo ✅ Fast execution
    echo ✅ Minimal footprint
    echo ✅ Basic diagnostics
    echo ✅ Quick scan
    echo.
    echo Options:
    echo [1] Run Utility.exe
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🚀 RUNNING QUICK TOOL
        echo Performing basic system check...
        echo.
        echo ✅ Starting Utility...
        start "" "Utility.exe"
        echo ✅ Tool completed successfully!
        echo.
        echo 📋 Basic system check finished.
    )
) else (
    echo ❌ Utility.exe not found!
)

echo.
pause