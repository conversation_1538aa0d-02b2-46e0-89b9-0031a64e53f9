#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Test script để kiểm tra việc lấy số dư ví
"""

import sys
import os
sys.path.append(os.path.dirname(os.path.abspath(__file__)))

from wallet import get_balance_from_address, detect_cryptocurrency_network

def test_balance_checking():
    """Test balance checking functionality"""
    print("🧪 Testing wallet balance checking...")
    print("=" * 50)
    
    # Test addresses (these are example addresses, some may have 0 balance)
    test_addresses = [
        # Bitcoin addresses
        {
            "address": "**********************************",  # Genesis block address
            "expected_network": "Bitcoin"
        },
        {
            "address": "******************************************",  # Bech32 address
            "expected_network": "Bitcoin"
        },
        # Ethereum addresses
        {
            "address": "******************************************",  # Ethereum Foundation
            "expected_network": "Ethereum"
        },
        {
            "address": "******************************************",  # Null address
            "expected_network": "Ethereum"
        },
        # Litecoin address
        {
            "address": "LTC1234567890abcdefghijklmnopqrstuvwxyz",  # Example LTC address
            "expected_network": "Litecoin"
        },
        # Dogecoin address
        {
            "address": "DH5yaieqoZN36fDVciNyRueRGvGLR3mr7L",  # Example DOGE address
            "expected_network": "Dogecoin"
        }
    ]
    
    results = []
    
    for i, test_case in enumerate(test_addresses, 1):
        address = test_case["address"]
        expected_network = test_case["expected_network"]
        
        print(f"\n🔍 Test {i}: {address[:20]}...")
        
        # Test network detection
        detected_network = detect_cryptocurrency_network(address)
        print(f"   Network Detection: {detected_network} (expected: {expected_network})")
        
        # Test balance checking
        try:
            balance_info = get_balance_from_address(address, detected_network)
            
            if balance_info:
                balance = balance_info.get("balance", "Unknown")
                currency = balance_info.get("currency", "Unknown")
                api_source = balance_info.get("api_source", "Unknown")
                has_balance = balance_info.get("has_balance", False)
                
                print(f"   ✅ Balance: {balance} {currency}")
                print(f"   📡 API Source: {api_source}")
                print(f"   💰 Has Balance: {has_balance}")
                
                results.append({
                    "address": address[:20] + "...",
                    "network": detected_network,
                    "balance": balance,
                    "currency": currency,
                    "api_source": api_source,
                    "has_balance": has_balance,
                    "status": "SUCCESS"
                })
            else:
                print(f"   ❌ No balance info returned")
                results.append({
                    "address": address[:20] + "...",
                    "network": detected_network,
                    "balance": "Unknown",
                    "currency": "Unknown",
                    "api_source": "Unknown",
                    "has_balance": False,
                    "status": "FAILED"
                })
                
        except Exception as e:
            print(f"   ❌ Error: {e}")
            results.append({
                "address": address[:20] + "...",
                "network": detected_network,
                "balance": "Error",
                "currency": "Error",
                "api_source": "Error",
                "has_balance": False,
                "status": "ERROR",
                "error": str(e)
            })
    
    # Summary
    print("\n" + "=" * 50)
    print("📊 BALANCE CHECKING TEST RESULTS")
    print("=" * 50)
    
    success_count = len([r for r in results if r["status"] == "SUCCESS"])
    failed_count = len([r for r in results if r["status"] == "FAILED"])
    error_count = len([r for r in results if r["status"] == "ERROR"])
    
    print(f"✅ Successful: {success_count}")
    print(f"❌ Failed: {failed_count}")
    print(f"🚫 Errors: {error_count}")
    print(f"📊 Total: {len(results)}")
    
    print("\n📋 Detailed Results:")
    for i, result in enumerate(results, 1):
        status_icon = "✅" if result["status"] == "SUCCESS" else "❌" if result["status"] == "FAILED" else "🚫"
        print(f"{i}. {status_icon} {result['address']} | {result['network']} | {result['balance']} {result['currency']} | {result['api_source']}")
        if "error" in result:
            print(f"   Error: {result['error']}")
    
    # Test with zero balance scenario
    print("\n" + "=" * 50)
    print("🧪 Testing Zero Balance Handling")
    print("=" * 50)
    
    # Create a mock balance info with zero balance
    zero_balance_info = {
        "balance": 0.0,
        "currency": "BTC",
        "api_source": "Test",
        "has_balance": False
    }
    
    print("Testing zero balance formatting:")
    balance = zero_balance_info.get("balance", "Unknown")
    currency = zero_balance_info.get("currency", "")
    
    if isinstance(balance, (int, float)):
        if balance > 0:
            balance_display = f"{balance:.6f} {currency}"
        else:
            balance_display = f"0.000000 {currency}"
    else:
        balance_display = "Unknown"
    
    print(f"✅ Zero balance display: {balance_display}")
    print(f"✅ Has balance flag: {zero_balance_info['has_balance']}")
    
    return results

def main():
    """Main function"""
    print("🎯 Wallet Balance Testing Tool")
    print("Testing balance checking functionality...")
    
    try:
        results = test_balance_checking()
        
        print("\n🎯 Test completed!")
        print("Check the results above to see if balance checking is working correctly.")
        
        # Check if we can get any balance info
        successful_results = [r for r in results if r["status"] == "SUCCESS"]
        if successful_results:
            print(f"\n✅ Balance checking is working! Got {len(successful_results)} successful results.")
        else:
            print(f"\n⚠️ No successful balance checks. This might be due to:")
            print("   - Network connectivity issues")
            print("   - API rate limiting")
            print("   - Invalid test addresses")
            print("   - API endpoints being down")
        
    except Exception as e:
        print(f"\n❌ Test failed with error: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    main()
