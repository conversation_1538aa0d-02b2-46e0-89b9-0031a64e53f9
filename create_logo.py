#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
Create TradingView logo.ico file
"""

try:
    from PIL import Image, ImageDraw, ImageFont
    import os
    
    def create_tradingview_logo():
        """Create a simple TradingView logo"""
        # Create a 256x256 image with transparent background
        size = 256
        img = Image.new('RGBA', (size, size), (0, 0, 0, 0))
        draw = ImageDraw.Draw(img)
        
        # TradingView colors
        bg_color = (19, 23, 34)  # Dark blue background
        accent_color = (0, 150, 255)  # Blue accent
        text_color = (255, 255, 255)  # White text
        
        # Draw background circle
        margin = 20
        draw.ellipse([margin, margin, size-margin, size-margin], fill=bg_color)
        
        # Draw "TV" text
        try:
            # Try to use a system font
            font_size = 80
            font = ImageFont.truetype("arial.ttf", font_size)
        except:
            # Fallback to default font
            font = ImageFont.load_default()
        
        text = "TV"
        bbox = draw.textbbox((0, 0), text, font=font)
        text_width = bbox[2] - bbox[0]
        text_height = bbox[3] - bbox[1]
        
        x = (size - text_width) // 2
        y = (size - text_height) // 2 - 10
        
        draw.text((x, y), text, fill=text_color, font=font)
        
        # Draw accent line
        line_y = y + text_height + 10
        line_margin = 60
        draw.rectangle([line_margin, line_y, size-line_margin, line_y+4], fill=accent_color)
        
        # Save as ICO
        img.save('logo.ico', format='ICO', sizes=[(256, 256), (128, 128), (64, 64), (32, 32), (16, 16)])
        print("✅ logo.ico created successfully!")
        return True
        
    if __name__ == "__main__":
        if create_tradingview_logo():
            print("📁 logo.ico file is ready for use")
        else:
            print("❌ Failed to create logo.ico")
            
except ImportError:
    print("❌ PIL (Pillow) not available. Installing...")
    import subprocess
    import sys
    
    try:
        subprocess.check_call([sys.executable, "-m", "pip", "install", "Pillow"])
        print("✅ Pillow installed. Please run this script again.")
    except:
        print("❌ Failed to install Pillow. Please install manually: pip install Pillow")
